"""
General Q&A agent with LangGraph workflow - direct LLM passthrough
"""
from typing import Optional, AsyncGenerator, Union, Dict, Any, List
import json
import time

from langgraph.graph import StateGraph, END

from app.agents.base import BaseAgent, AgentWorkflowState
from app.core.models import AgentType, TaskStatus
from app.services.llm_service import llm_service
from app.services.web_search_service import web_search_service
from app.tools.search_tools import get_tool_schemas, execute_tool
from app.utils.logger import get_logger
from app.config import settings
from app.agents.content.context_utils import (
    estimate_messages_tokens,
    fit_messages_into_budget,
    truncate_messages_into_budget,
)
from app.agents.qa.prompts import (
    CHAT_SYSTEM_PROMPT,
    build_chat_messages
)

logger = get_logger(__name__)


class GeneralQAAgent(BaseAgent):
    """General Q&A agent with conversation history management and LLM passthrough"""

    def __init__(self, agent_type: AgentType):
        super().__init__(
            agent_type=agent_type,
            name="General Q&A Agent",
            description="Handles general Q&A with conversation history support and returns raw LLM responses"
        )

    def _build_graph(self):
        """Build LangGraph workflow for Q&A processing"""
        workflow = StateGraph(AgentWorkflowState)

        # Add processing nodes
        workflow.add_node("validate_input", self._validate_input_node)
        workflow.add_node("truncate_history", self._truncate_history_node)
        workflow.add_node("preprocess_search", self._preprocess_search_node)
        workflow.add_node("check_tools_llm_call", self._check_tools_llm_call_node)  # 第一次调用：检查工具
        workflow.add_node("execute_tools", self._execute_tools_node)
        workflow.add_node("final_llm_call", self._final_llm_call_node)  # 第二次调用：生成最终响应
        workflow.add_node("finalize", self._finalize_node)

        # Define workflow edges
        workflow.set_entry_point("validate_input")
        # route based on input type
        workflow.add_conditional_edges(
            "validate_input",
            self._route_after_validation,
            {
                "truncate_history": "truncate_history",
            },
        )
        
        # 先进行预处理搜索判断，再进行第一次LLM调用（检查工具）
        workflow.add_edge("truncate_history", "preprocess_search")
        workflow.add_edge("preprocess_search", "check_tools_llm_call")
        
        # 第一次LLM调用后的路由：检查是否需要执行工具
        workflow.add_conditional_edges(
            "check_tools_llm_call",
            self._route_after_check_tools,
            {
                "execute_tools": "execute_tools",
                "final_llm_call": "final_llm_call",
                "finalize": "finalize"
            }
        )
        
        # 工具执行后的路由：检查是否需要流式输出
        workflow.add_conditional_edges(
            "execute_tools",
            self._route_after_tool_execution,
            {
                "final_llm_call": "final_llm_call",
                "finalize": "finalize"
            }
        )
        
        # 第二次LLM调用后直接完成
        workflow.add_edge("final_llm_call", "finalize")
        workflow.add_edge("finalize", END)

        # Compile the graph
        self.graph = workflow.compile()


    async def process_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Required by BaseAgent interface"""
        return state

    async def _validate_input_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate and prepare input data for Q&A"""
        try:
            state["current_step"] = "validate_input"
            input_data = state["input_data"]

            messages = input_data.get("messages", [])
            model = input_data.get("model")
            is_stream = input_data.get("stream", False)
            max_tokens = input_data.get("max_tokens") or settings.default_max_tokens

            # Validate messages is not empty
            if not messages or len(messages) == 0:
                raise ValueError("messages must be a non-empty list")

            # Store validated data
            state["step_results"]["messages"] = messages
            state["step_results"]["model"] = model
            state["step_results"]["is_stream"] = is_stream
            state["step_results"]["max_tokens"] = max_tokens
            
            # Handle search options - 保存搜索配置供工具使用
            search_options = input_data.get("searchOptions")
            state["step_results"]["search_options"] = search_options



        except Exception as e:
            state["error"] = f"Input validation failed: {str(e)}"
            logger.error(f"Input validation failed: {e}")

        return state






    async def _truncate_history_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Truncate history by tokens and ensure system prompt is present"""
        try:
            state["current_step"] = "truncate_history"
            step_results = state["step_results"]
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            model = step_results.get("model")
            incoming_messages = step_results.get("messages", [])
            search_options = step_results.get("search_options")

            # Check if system message exists
            has_system_message = incoming_messages and incoming_messages[0].get("role") == "system"
            
            # 搜索工具使用指导
            search_tool_guidance = ""
            if search_options and search_options.get("enableInternetSearch", False):
                search_tool_guidance = """

## 🔧 工具使用能力

你现在可以使用智能搜索工具进行深度研究：

### ⚡ 必须使用搜索工具的场景
**遇到以下问题时，必须主动调用 web_search 工具：**
- 🌤️ **实时信息查询**：天气预报、股票价格、汇率、时间等
- 📰 **最新新闻事件**：当日新闻、突发事件、最新动态
- 📊 **实时数据**：市场行情、比赛结果、排行榜等
- 🏢 **公司最新信息**：产品发布、政策更新、组织变动
- 🔍 **知识补充**：当你的知识可能过时或不确定时
- 📚 **未知术语**：遇到没听过的专业术语、产品名称、系统名称等（优先使用wiki搜索内部资料）

**示例触发词**：今天、现在、最新、当前、实时、今年、2024年、2025年等

### web_search 工具 - 双重搜索源
**🏢 wiki（内部资料库）** - 优先搜索源
- 公司内部产品文档、项目资料
- 内部技术方案、系统架构  
- 工作流程、规范标准
- 内部培训材料

**🌐 外网搜索** - 补充搜索源  
- 最新新闻事件、市场趋势
- 公开产品信息、价格规格
- 技术教程、开源项目
- 行业分析、公开资料

### 🎯 搜索策略
1. **主动搜索**：遇到实时信息需求时，立即使用搜索工具，不要基于过时知识回答
2. **优先内部**：涉及公司业务时，优先使用wiki搜索内部资料
3. **智能组合**：可同时搜索多个源 `["wiki", "hiagent"]` 获取全面信息  
4. **场景判断**：
   - 天气、新闻、实时数据 → 外网搜索
   - 公司产品或工作相关问题 → 优先wiki
   - 未知术语、产品名称、系统名称 → 优先wiki搜索
   - 最新技术趋势 → 外网搜索  
   - 技术方案对比 → 同时搜索两个源
5. **精准关键词**：使用简洁、专业的搜索词
6. **引用标注**：明确标注信息来源（wiki/外网）

**重要提醒**：
1. 当用户询问任何可能需要实时信息的问题时（如天气、新闻、价格等），必须先使用搜索工具获取最新信息
2. 当遇到不熟悉的术语、产品名称、系统名称时，优先使用wiki搜索，因为这些很可能是公司内部专用术语
3. 先搜索获取准确信息，再基于搜索结果回答用户问题"""
            
            if search_options and search_options.get("enableInternetSearch", False):
                # 当启用联网搜索时，需要增强system prompt
                if has_system_message:
                    # 如果用户提供了system message，在其后追加搜索工具指导
                    system_message = incoming_messages[0]
                    enhanced_content = system_message["content"] + search_tool_guidance
                    incoming_messages[0] = {"role": "system", "content": enhanced_content}
                    logger.info("在用户system message后追加联网搜索增强")
                else:
                    # 如果用户没有提供system message，添加包含联网搜索增强的默认system prompt
                    enhanced_system_prompt = CHAT_SYSTEM_PROMPT + search_tool_guidance
                    incoming_messages = [{"role": "system", "content": enhanced_system_prompt}] + incoming_messages
                    logger.info("添加包含联网搜索增强的默认system prompt")
            else:
                # 未启用联网搜索时，保持原有逻辑
                if not has_system_message:
                    # 只在没有system message时添加默认的
                    incoming_messages = [{"role": "system", "content": CHAT_SYSTEM_PROMPT}] + incoming_messages
                    logger.info("添加默认system prompt（无联网搜索增强）")

            context_window = settings.get_context_window(model)
            safety_margin = 300
            allowed_input_tokens = max(1000, context_window - max_tokens - safety_margin)

            processed_messages, token_count = truncate_messages_into_budget(
                incoming_messages, allowed_input_tokens
            )
            state["step_results"]["processed_messages"] = processed_messages
            state["step_results"]["prompt_tokens"] = token_count
            
            search_status = "启用搜索工具" if search_options and search_options.get("enableInternetSearch", False) else "普通对话模式"
            logger.info(
                f"Messages processing completed - {search_status}, {len(processed_messages)} messages, tokens≈{token_count}"
            )
        except Exception as e:
            state["error"] = f"Truncate history failed: {str(e)}"
            logger.error(f"Truncate history failed: {e}")
        return state

    def _route_after_validation(self, state: AgentWorkflowState) -> str:
        """Route based on input type - simplified for tool-based approach"""
        # All inputs are now messages, so we always use truncate_history
        return "truncate_history"

    async def _preprocess_search_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """预处理搜索：分析问题并提前执行搜索，将结果加入context"""
        try:
            state["current_step"] = "preprocess_search"
            step_results = state["step_results"]
            
            messages = step_results.get("processed_messages", [])
            search_options = step_results.get("search_options")
            
            # 检查是否启用搜索功能
            if not (search_options and search_options.get("enableInternetSearch", False)):
                logger.info("搜索功能未启用，跳过预处理搜索")
                step_results["has_search_results"] = False
                return state
            
            # 使用完整的messages进行LLM判断
            if not messages:
                logger.info("消息列表为空，跳过预处理搜索")
                step_results["has_search_results"] = False
                return state
            
            # 使用LLM智能判断是否需要搜索（使用完整messages）
            logger.info(f"开始LLM搜索判断，消息数量: {len(messages)}")
            needs_search, search_query, search_provider = await self._analyze_search_need_with_llm(messages)
            logger.info(f"LLM判断完成: needs_search={needs_search}, query='{search_query}', provider={search_provider}")
            
            if needs_search:
                logger.info(f"检测到需要搜索: query='{search_query}', provider={search_provider}")
                
                # 执行搜索
                logger.info(f"开始执行搜索: query='{search_query}', provider={search_provider}")
                search_results = await web_search_service.search(
                    query=search_query,
                    provider=search_provider,
                    snippet=False,
                    authInfos=search_options.get("authInfos")
                )
                logger.info(f"搜索完成，获得 {len(search_results) if search_results else 0} 条结果")
                
                if search_results:
                    # 格式化搜索结果
                    search_context = web_search_service.format_search_results(search_results, max_results=5)
                    logger.info(f"格式化搜索结果，长度: {len(search_context)} 字符")
                    
                    # 将搜索结果作为系统消息加入到对话中
                    search_message = {
                        "role": "system", 
                        "content": f"**实时搜索结果**（请基于以下最新信息回答用户问题）：\n\n{search_context}"
                    }
                    
                    # 插入到最后一条用户消息前
                    original_count = len(messages)
                    messages.insert(-1, search_message)
                    step_results["processed_messages"] = messages
                    step_results["has_search_results"] = True
                    
                    logger.info(f"成功将搜索结果加入context: 消息数从 {original_count} 增加到 {len(messages)}")
                    logger.info(f"搜索结果预览: {search_context[:200]}...")
                else:
                    logger.warning("搜索未返回结果")
                    step_results["has_search_results"] = False
            else:
                logger.info("问题不需要实时搜索，继续正常处理")
                step_results["has_search_results"] = False
            
        except Exception as e:
            logger.error(f"预处理搜索失败: {e}", exc_info=True)
            # 搜索失败不影响正常对话，继续执行
            step_results["has_search_results"] = False
            
        return state

    async def _analyze_search_need_with_llm(self, messages: list) -> tuple[bool, str, list]:
        """使用LLM分析消息历史，判断是否需要搜索以及搜索策略"""
        
        # 构建判断prompt作为系统消息
        judge_system_prompt = """你是一个搜索需求分析助手。分析用户的对话是否需要联网搜索。

分析规则：
1. 天气、股价、新闻等实时信息 → 需要搜索
2. 包含"今天"、"最新"、"现在"等时间词 → 需要搜索  
3. 公司内部术语、产品名称 → 需要搜索wiki
4. 基础常识、创意任务 → 不需要搜索

请严格按照以下JSON格式回答（不要添加任何其他内容）：

{
    "need_search": true,
    "reason": "包含今天和天气，需要实时信息",
    "search_query": "南京天气", 
    "providers": ["hiagent"]
}

或

{
    "need_search": false,
    "reason": "基础常识问题",
    "search_query": "",
    "providers": []
}"""

        try:
            # 构建判断用的messages（在原messages基础上添加判断任务）
            judge_messages = [{"role": "system", "content": judge_system_prompt}]
            
            # 添加用户的对话历史
            for msg in messages:
                if msg.get("role") in ["user", "assistant"]:  # 只包含用户和助手的对话
                    judge_messages.append(msg)
            
            # 添加判断指令
            judge_messages.append({
                "role": "user", 
                "content": "请根据以上对话分析是否需要联网搜索，并按JSON格式回答。"
            })
            
            response = await llm_service.chat_completion(
                messages=judge_messages,
                model=None,  # 使用默认模型
                max_tokens=200,
                temperature=0.1  # 降低温度确保稳定输出
            )
            
            if response and "choices" in response:
                content = response["choices"][0]["message"]["content"].strip()
                logger.info(f"LLM搜索判断原始响应: {content}")
                
                # 尝试解析JSON响应
                import re
                import json
                
                # 提取JSON内容（更宽松的匹配）
                json_str = content.strip()
                
                # 尝试直接解析
                if json_str.startswith('{') and json_str.endswith('}'):
                    pass  # 直接使用
                else:
                    # 查找JSON块
                    json_match = re.search(r'\{[^{}]*"need_search"[^{}]*\}', content, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(0)
                    else:
                        logger.warning(f"无法从LLM响应中提取JSON: {content}")
                        return False, "", []
                
                try:
                    result = json.loads(json_str)
                    
                    need_search = result.get("need_search", False)
                    search_query = result.get("search_query", "").strip()
                    providers = result.get("providers", ["hiagent"])
                    reason = result.get("reason", "")
                    
                    logger.info(f"LLM搜索判断结果: need_search={need_search}, query='{search_query}', providers={providers}, reason='{reason}'")
                    
                    return need_search, search_query, providers
                    
                except json.JSONDecodeError as e:
                    logger.error(f"解析LLM搜索判断JSON失败: {e}, content: {json_str}")
                    return False, "", []
            else:
                logger.error("LLM搜索判断调用失败")
                return False, "", []
                
        except Exception as e:
            logger.error(f"LLM搜索判断异常: {e}", exc_info=True)
            # 失败时回退到保守策略：不搜索
            return False, "", []

    async def _check_tools_llm_call_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """第一次LLM调用：始终用非流式模式检查是否需要工具调用"""
        try:
            state["current_step"] = "check_tools_llm_call"
            step_results = state["step_results"]
            
            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")
            # 保存用户要求的流式模式设置
            user_requested_stream = step_results.get("is_stream", False)
            step_results["user_requested_stream"] = user_requested_stream
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            
            # 检查是否已经完成预处理搜索
            has_search_results = step_results.get("has_search_results", False)
            search_options = step_results.get("search_options")
            
            
            # 如果已经完成预处理搜索，则不再需要工具调用
            if has_search_results:
                logger.info("✅ 已完成预处理搜索，跳过工具调用模式")
                tools = None
            elif search_options and search_options.get("enableInternetSearch", False):
                # 启用工具调用来检查是否需要搜索
                tools = get_tool_schemas()
                logger.info("⚡ 启用工具调用检查")
            else:
                logger.info("❌ 未启用搜索功能")
                tools = None
            
            # 第一次调用：始终使用非流式模式来检查工具调用
            llm_response = await llm_service.chat_completion(
                messages=messages,
                model=model,
                max_tokens=max_tokens,
                temperature=0.7,
                tools=tools
            )
            
            # 检查响应
            if llm_response and isinstance(llm_response, dict) and "choices" in llm_response:
                choices = llm_response["choices"]
                if choices:
                    choice = choices[0]
                    if "message" in choice:
                        message = choice["message"]
                        content = message.get("content", "")
                        tool_calls = message.get("tool_calls")
                        
                        # 保存响应信息
                        state["step_results"]["first_llm_response"] = llm_response
                        if tool_calls:
                            logger.info(f"检测到工具调用: {len(tool_calls)}")
                            state["step_results"]["tool_calls"] = tool_calls
                            state["step_results"]["assistant_message"] = message
                        else:
                            # 如果没有工具调用，保存第一次的响应用于可能的直接输出
                            state["step_results"]["direct_response"] = llm_response
            else:
                logger.error("第一次LLM响应格式异常")
                state["step_results"]["first_llm_response"] = llm_response
            
        except Exception as e:
            state["error"] = f"LLM call failed: {str(e)}"
            logger.error(f"LLM call failed: {e}")
            
        return state

    def _route_after_check_tools(self, state: AgentWorkflowState) -> str:
        """第一次LLM调用后的路由：决定是否需要工具调用"""
        step_results = state.get("step_results", {})
        
        # 检查是否有工具调用请求
        if step_results.get("tool_calls"):
            return "execute_tools"
        else:
            # 没有工具调用，检查用户是否要求流式输出
            user_requested_stream = step_results.get("user_requested_stream", False)
            if user_requested_stream:
                return "final_llm_call"
            else:
                return "finalize"

    def _route_after_tool_execution(self, state: AgentWorkflowState) -> str:
        """工具执行后的路由：决定是否需要流式输出"""
        step_results = state.get("step_results", {})
        
        # 检查用户是否要求流式输出
        user_requested_stream = step_results.get("user_requested_stream", False)
        if user_requested_stream:
            return "final_llm_call"
        else:
            return "finalize"

    async def _execute_tools_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """执行 LLM 请求的工具"""
        try:
            state["current_step"] = "execute_tools"
            step_results = state["step_results"]
            tool_calls = step_results.get("tool_calls", [])
            search_options = step_results.get("search_options", {})
            
            logger.info(f"⚡ 开始执行 {len(tool_calls)} 个工具调用")
            
            tool_results = []
            for i, tool_call in enumerate(tool_calls, 1):
                function = tool_call.get("function", {})
                tool_name = function.get("name")
                tool_args_str = function.get("arguments", "{}")
                tool_call_id = tool_call.get("id", "")
                
                try:
                    tool_args = json.loads(tool_args_str)
                    logger.info(f"🔧 执行工具 {i}/{len(tool_calls)}: {tool_name}")
                    logger.info(f"   参数: {tool_args}")
                    
                    # 为搜索工具添加搜索配置
                    if tool_name == "web_search":
                        tool_args["search_options"] = search_options
                    
                    # 执行工具
                    result = await execute_tool(tool_name, **tool_args)
                    
                    tool_results.append({
                        "tool_call_id": tool_call_id,
                        "role": "tool",
                        "name": tool_name,
                        "content": json.dumps(result, ensure_ascii=False, indent=2)
                    })
                    
                    if result.get("success"):
                        logger.info(f"✅ 工具 {tool_name} 执行成功")
                    else:
                        logger.warning(f"⚠️ 工具 {tool_name} 执行完成但有警告")
                    
                except Exception as e:
                    error_result = {
                        "success": False,
                        "error": f"工具执行失败: {str(e)}"
                    }
                    tool_results.append({
                        "tool_call_id": tool_call_id,
                        "role": "tool", 
                        "name": tool_name,
                        "content": json.dumps(error_result, ensure_ascii=False)
                    })
                    logger.error(f"❌ 工具 {tool_name} 执行失败: {e}")
            
            # 将工具结果添加到消息历史
            messages = step_results.get("processed_messages", [])
            assistant_message = step_results.get("assistant_message")
            
            if assistant_message:
                messages.append(assistant_message)
            
            messages.extend(tool_results)
            state["step_results"]["processed_messages"] = messages
            state["step_results"]["tool_results"] = tool_results
            
            # 清除工具调用状态，准备下一轮LLM调用
            state["step_results"]["tool_calls"] = None
            state["step_results"]["assistant_message"] = None

            
        except Exception as e:
            state["error"] = f"工具执行失败: {str(e)}"
            logger.error(f"工具执行失败: {e}")
            
        return state

    async def _final_llm_call_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """第二次LLM调用：始终使用流式模式生成最终响应"""
        try:
            state["current_step"] = "final_llm_call"
            step_results = state["step_results"]
            
            # 获取处理后的消息（可能包含工具结果）
            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            
            
            # 第二次调用：始终使用流式模式（不需要工具调用）
            stream_generator = llm_service.stream_chat_completion(
                messages=messages,
                model=model,
                max_tokens=max_tokens,
                temperature=0.7,
                tools=None  # 第二次调用不需要工具
            )
            
            # 保存流式生成器
            state["step_results"]["llm_stream"] = stream_generator
            
        except Exception as e:
            state["error"] = f"Final LLM call failed: {str(e)}"
            logger.error(f"Final LLM call failed: {e}")
            
        return state

    async def _perform_final_non_stream_call(self, state: AgentWorkflowState):
        """执行工具后的非流式最终LLM调用"""
        try:
            step_results = state["step_results"]
            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            
            # 使用非流式调用
            final_response = await llm_service.chat_completion(
                messages=messages,
                model=model,
                max_tokens=max_tokens,
                temperature=0.7,
                tools=None  # 最终调用不需要工具
            )
            
            state["final_output"] = final_response
            
        except Exception as e:
            logger.error(f"非流式最终调用失败: {e}", exc_info=True)
            # 回退到第一次响应
            state["final_output"] = step_results.get("first_llm_response", {})

    async def _call_llm_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Call LLM and store response (streaming or non-streaming)"""
        try:
            state["current_step"] = "call_llm"
            step_results = state["step_results"]

            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")  # Get custom model
            is_stream = step_results.get("is_stream", False)
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)

            if is_stream:
                # For streaming: create and store the stream generator
                stream_generator = llm_service.stream_chat_completion(
                    messages=messages,
                    model=model,  # Pass custom model
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                state["step_results"]["llm_stream"] = stream_generator
            else:
                # For non-streaming: get the complete response
                llm_response = await llm_service.chat_completion(
                    messages=messages,
                    model=model,  # Pass custom model
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                state["step_results"]["llm_response"] = llm_response

        except Exception as e:
            state["error"] = f"LLM call failed: {str(e)}"
            logger.error(f"LLM call failed: {e}")

        return state

    async def _finalize_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Finalize and return appropriate response based on workflow path"""
        try:
            state["current_step"] = "finalize"
            step_results = state["step_results"]

            user_requested_stream = step_results.get("user_requested_stream", False)
            tool_results = step_results.get("tool_results", [])

            # 根据流程决定返回什么
            if step_results.get("llm_stream"):
                # 有流式生成器（来自第二次LLM调用）
                state["final_output"] = step_results.get("llm_stream")
            elif tool_results and not user_requested_stream:
                # 有工具结果但用户不要求流式，需要进行非流式的第二次LLM调用
                logger.info("工具执行完成，进行非流式第二次LLM调用")
                await self._perform_final_non_stream_call(state)
            elif step_results.get("direct_response"):
                # 没有工具调用，用户不要求流式，直接返回第一次的响应
                state["final_output"] = step_results.get("direct_response", {})
            elif step_results.get("first_llm_response"):
                # 回退到第一次响应
                state["final_output"] = step_results.get("first_llm_response", {})
            else:
                # 完全回退
                logger.error("没有找到任何有效响应")
                state["final_output"] = step_results.get("llm_response", {})

        except Exception as e:
            state["error"] = f"Finalization failed: {str(e)}"
            logger.error(f"Finalization failed: {e}", exc_info=True)

        return state



    # Direct access methods for API usage
    async def process_chat(self,
                           messages: List[Dict[str, str]],
                           model: Optional[str] = None,
                           stream: bool = False,
                           max_tokens: Optional[int] = None,
                           searchOptions: Optional[Dict[str, Any]] = None) -> Union[Any, AsyncGenerator[str, None]]:
        """Q&A processing with smart conversation history management"""
        
        # Prepare input data for LangGraph workflow
        input_data = {
            "messages": messages,
            "model": model,
            "stream": stream,
            "max_tokens": max_tokens or settings.default_max_tokens,
            "searchOptions": searchOptions,
        }

        # Execute the LangGraph workflow
        agent_state = await self.execute(input_data)
        
        if agent_state.status == TaskStatus.FAILED:
            # Return error in appropriate format
            error_response = {"error": agent_state.error_message}
            if stream:
                async def error_stream():
                    yield f"data: {json.dumps(error_response)}"
                return error_stream()
            else:
                return error_response

        # Return the result from workflow (either stream generator or response data)
        return agent_state.output_data
