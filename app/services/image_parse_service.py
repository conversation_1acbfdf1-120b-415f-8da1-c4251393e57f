"""
图片解析服务
"""
import aiohttp
import json
import base64
from typing import Dict, Any, Optional
from dataclasses import dataclass

from app.config import settings
from app.services.llm_service import LLMClient
from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ImageParseRequest:
    """图片解析请求数据结构"""
    url: str
    question: str = "描述这个图片"  # 默认问题，可以自定义


@dataclass
class ImageParseResult:
    """图片解析结果数据结构"""
    success: bool
    output: str
    message: str


class ImageParseClient:
    """图片解析客户端 - 使用LLM视觉模型"""

    def __init__(self):
        self.session = None
        self._timeout = aiohttp.ClientTimeout(total=180)  # 3分钟超时
        self.vision_model = "ht::saas-doubao-1.5-vl-pro-32k"  # 强制使用指定的视觉模型
        
        # 为图片解析创建专用的LLM客户端，使用专门的API地址
        self.vision_api_url = "http://168.64.26.85/web/unauth/LLM_api_proxy/v1/chat/completions"
        self.llm_client = LLMClient(
            api_url=self.vision_api_url,
            default_model=self.vision_model
        )
        logger.info(f"初始化图片解析专用LLM客户端: {self.vision_api_url}")

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self._timeout)
        return self.session

    async def _download_image_to_base64(self, url: str) -> str:
        """下载图片并转换为base64编码"""
        session = await self._get_session()
        
        try:
            logger.info(f"开始下载图片: {url}")
            async with session.get(url) as response:
                if response.status != 200:
                    raise Exception(f"图片下载失败: HTTP {response.status}")
                
                image_data = await response.read()
                # 将图片数据编码为base64
                base64_data = base64.b64encode(image_data).decode('utf-8')
                # 获取内容类型
                content_type = response.headers.get('content-type', 'image/jpeg')
                # 构造data URL格式
                base64_url = f"data:{content_type};base64,{base64_data}"
                
                logger.info(f"图片下载并转换为base64成功，大小: {len(base64_data)} bytes")
                return base64_url
                
        except Exception as e:
            logger.error(f"图片下载失败: {str(e)}")
            raise

    async def parse_image(self, request: ImageParseRequest) -> ImageParseResult:
        """使用LLM视觉模型执行图片解析"""
        try:
            logger.info(f"开始使用LLM解析图片: {request.url}")
            
            # 将图片URL转换为base64
            base64_image = await self._download_image_to_base64(request.url)
            
            # 构造LLM请求消息，格式按照用户要求
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": request.question
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": base64_image
                            }
                        }
                    ]
                }
            ]
            
            logger.info(f"=== LLM图片解析请求调试信息 ===")
            logger.info(f"模型: {self.vision_model}")
            logger.info(f"消息结构: {json.dumps(messages, ensure_ascii=False, indent=2)[:500]}...")
            
            # 调用专用的LLM客户端
            from app.services.llm_service import LLMRequest
            llm_request = LLMRequest(
                messages=messages,
                model=self.vision_model,
                max_tokens=2000,
                temperature=0.7,
                stream=False
            )
            llm_response = await self.llm_client.chat_completion(llm_request)
            
            logger.info(f"=== LLM响应调试信息 ===")
            logger.info(f"响应类型: {type(llm_response)}")
            logger.info(f"响应内容: {json.dumps(llm_response, ensure_ascii=False, indent=2)[:500]}...")
            
            # 解析LLM响应
            parsed_result = self._parse_llm_response(llm_response)
            logger.info(f"LLM图片解析完成: success={parsed_result.success}")
            return parsed_result
                    
        except Exception as e:
            logger.error(f"LLM图片解析执行异常: {str(e)}", exc_info=True)
            return ImageParseResult(
                success=False,
                output="",
                message=f"LLM解析异常: {str(e)}"
            )

    def _parse_llm_response(self, response: Dict[str, Any]) -> ImageParseResult:
        """解析LLM响应为标准格式"""
        logger.info(f"=== 开始解析LLM响应结果 ===")
        logger.info(f"响应类型: {type(response)}")
        logger.info(f"响应键: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
        
        try:
            # 标准的LLM chat completion响应格式
            if "choices" in response and isinstance(response["choices"], list) and len(response["choices"]) > 0:
                choice = response["choices"][0]
                
                # 检查是否有错误
                if "error" in response:
                    error_info = response["error"]
                    error_message = error_info.get("message", "未知错误") if isinstance(error_info, dict) else str(error_info)
                    logger.error(f"LLM响应包含错误: {error_message}")
                    return ImageParseResult(
                        success=False,
                        output="",
                        message=f"LLM处理错误: {error_message}"
                    )
                
                # 提取消息内容
                if "message" in choice and "content" in choice["message"]:
                    content = choice["message"]["content"]
                    logger.info(f"成功提取LLM响应内容，长度: {len(content)}")
                    
                    return ImageParseResult(
                        success=True,
                        output=content,
                        message="图片解析成功"
                    )
                else:
                    logger.warning("LLM响应中没有找到message.content字段")
                    return ImageParseResult(
                        success=False,
                        output="",
                        message="LLM响应格式异常：缺少content字段"
                    )
            
            # 检查是否是错误响应
            elif "error" in response:
                error_info = response["error"]
                error_message = error_info.get("message", "未知错误") if isinstance(error_info, dict) else str(error_info)
                logger.error(f"LLM返回错误响应: {error_message}")
                return ImageParseResult(
                    success=False,
                    output="",
                    message=f"LLM服务错误: {error_message}"
                )
            
            else:
                logger.warning("LLM响应格式不符合预期")
                logger.warning(f"响应内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
                return ImageParseResult(
                    success=False,
                    output="",
                    message="LLM响应格式异常"
                )
                        
        except Exception as e:
            logger.error(f"解析LLM响应失败: {str(e)}", exc_info=True)
            logger.error(f"响应内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
            return ImageParseResult(
                success=False,
                output="",
                message=f"响应解析异常: {str(e)}"
            )

    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
        
        # 关闭专用的LLM客户端
        if hasattr(self, 'llm_client') and self.llm_client:
            await self.llm_client.close()


class ImageParseService:
    """图片解析服务管理器 - 使用LLM视觉模型"""

    def __init__(self):
        self.client = ImageParseClient()
        logger.info("初始化图片解析服务 - 使用LLM视觉模型")

    async def parse_image(self, url: str, question: str = "描述这个图片") -> ImageParseResult:
        """执行图片解析"""
        logger.info(f"=== ImageParseService.parse_image 调用 ===")
        logger.info(f"图片URL: {url}")
        logger.info(f"问题: {question}")
        
        request = ImageParseRequest(url=url, question=question)
        
        result = await self.client.parse_image(request)
        logger.info(f"ImageParseService.parse_image 返回结果: success={result.success}")
        return result

    async def close(self):
        """关闭服务"""
        await self.client.close()


# 全局图片解析服务实例
image_parse_service = ImageParseService()
