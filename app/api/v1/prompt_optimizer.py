"""
提示词优化API - LangGraph agent with direct result passthrough
"""
from fastapi import APIRouter
from fastapi.responses import JSONResponse, StreamingResponse

from app.agents.base import agent_registry
from app.api.models import PromptOptimizerRequest
from app.core.models import AgentType
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/prompt-optimizer", 
             summary="Prompt Optimization",
             description="优化用户提供的提示词，使其更加清晰和有效；支持流式和非流式响应模式。")
async def optimize_prompt(request: PromptOptimizerRequest):
    """提示词优化，使用LangGraph处理并直接透传结果"""
    
    logger.info(f"Processing prompt optimization request: question_length={len(request.question)}, stream={request.stream}, model={request.model}")

    # 创建提示词优化智能体
    agent = agent_registry.create_agent(AgentType.PROMPT_OPTIMIZER)

    # 使用智能体的直接处理方法
    result = await agent.process_optimization(
        question=request.question,
        stream=request.stream,
        model=request.model
    )

    if request.stream:
        # 返回正确格式化的SSE流
        async def stream_generator():
            try:
                async for line in result:
                    yield f"{line}\n\n"
            except Exception as e:
                logger.error(f"流式生成异常: {e}", exc_info=True)

        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )
    else:
        # 返回原始结果
        return JSONResponse(status_code=200, content=result)
